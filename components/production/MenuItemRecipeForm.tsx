"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Loader2, Plus, X, ChefHat, Package } from "lucide-react";
import { useStockV4 } from '@/lib/hooks/useStockV4';
import { useCOGSV4 } from "@/lib/hooks/useCOGSV4";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { SubRecipeForm } from "./SubRecipeForm";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";

const menuItemRecipeSchema = z.object({
  menuItemId: z.string().min(1, { message: "Veuillez sélectionner un article du menu." }),
  size: z.string().optional(),
  fixedCost: z.coerce.number().min(0, { message: "Le coût fixe doit être positif ou nul." }).optional().default(0),
  costingMethod: z.enum(["recipe", "fixed"]).default("fixed"),
  isRecipeLocked: z.boolean().default(false),
  ingredients: z.array(
    z.object({
      type: z.enum(["stock", "subRecipe"]),
      stockItemId: z.string().optional(),
      subRecipeId: z.string().optional(),
      quantity: z.coerce.number().positive({ message: "La quantité doit être positive." }),
    }).refine(data => {
      if (data.type === "stock") {
        return !!data.stockItemId;
      } else {
        return !!data.subRecipeId;
      }
    }, {
      message: "Veuillez sélectionner un ingrédient ou une sous-recette.",
      path: ["stockItemId"]
    })
  ).optional(),
}).refine((data) => {
  console.log('[Zod Validation] Validating data:', data);
  
  // Only require ingredients if using recipe costing method
  if (data.costingMethod === "recipe") {
    const isValid = data.ingredients && data.ingredients.length > 0;
    console.log('[Zod Validation] Recipe method - ingredients valid:', isValid);
    return isValid;
  }
  
  // For fixed cost method, require fixedCost to be provided and > 0
  if (data.costingMethod === "fixed") {
    const isValid = typeof data.fixedCost === "number" && data.fixedCost > 0;
    console.log('[Zod Validation] Fixed method - cost valid:', isValid, 'fixedCost:', data.fixedCost);
    return isValid;
  }
  
  console.log('[Zod Validation] Default case - returning true');
  return true;
}, {
  message: "Pour la méthode 'Calculé depuis la recette', des ingrédients sont requis. Pour 'Coût fixe', un montant supérieur à 0 doit être spécifié.",
  path: ["fixedCost"]
});

interface MenuItem {
  id: string;
  name: string;
  categoryName: string;
  sizes: string[];
  prices: Record<string, number>;
}

interface MenuItemRecipeFormProps {
  onSubmit: (data: any) => void;
  onCancel: () => void;
  initialData?: any | null;
  menuItems: MenuItem[];
  subRecipes: any[];
  existingRecipes?: any[];
  selectedSize?: string;
  isMobile?: boolean;
}

export function MenuItemRecipeForm({ 
  onSubmit, 
  onCancel, 
  initialData, 
  menuItems, 
  subRecipes, 
  existingRecipes = [],
  selectedSize = "",
  isMobile = false 
}: MenuItemRecipeFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { stockItems } = useStockV4();
  const { createSubRecipe } = useCOGSV4();
  const { toast } = useToast();
  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem | null>(null);
  const [isSubRecipeDialogOpen, setIsSubRecipeDialogOpen] = useState(false);

  const form = useForm<z.infer<typeof menuItemRecipeSchema>>({
    resolver: zodResolver(menuItemRecipeSchema),
    defaultValues: initialData
      ? {
          menuItemId: initialData.menuItemId,
          size: initialData.size || "",
          fixedCost: initialData.fixedCost || 0,
          costingMethod: initialData.costingMethod || "fixed",
          isRecipeLocked: initialData.isRecipeLocked || false,
          ingredients: initialData.ingredients?.map((ingredient: any) => {
            if ('stockItemId' in ingredient) {
              return {
                type: "stock" as const,
                stockItemId: ingredient.stockItemId,
                quantity: ingredient.quantity,
              };
            } else if ('subRecipeId' in ingredient) {
              return {
                type: "subRecipe" as const,
                subRecipeId: ingredient.subRecipeId,
                quantity: ingredient.quantity,
              };
            } else {
              return ingredient;
            }
          }) || [],
        }
      : {
          menuItemId: menuItems[0]?.id || "",
          size: selectedSize,
          fixedCost: 0,
          costingMethod: "fixed",
          isRecipeLocked: false,
          ingredients: [],
        },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "ingredients",
  });

  useEffect(() => {
    const menuItemId = form.watch("menuItemId");
    if (menuItemId) {
      const menuItem = menuItems.find(item => item.id === menuItemId);
      setSelectedMenuItem(menuItem || null);
    } else {
      setSelectedMenuItem(null);
    }
  }, [form.watch("menuItemId"), menuItems]);

  // Initialize form with selected size and load correct recipe
  useEffect(() => {
    if (menuItems[0] && selectedSize) {
      const menuItemId = menuItems[0].id;
      const existingRecipe = existingRecipes.find(r => 
        r.menuItemId === menuItemId && r.size === selectedSize
      );
      
      if (existingRecipe) {
        // Load existing recipe data for this size
        form.reset({
          menuItemId: existingRecipe.menuItemId,
          size: existingRecipe.size || "",
          fixedCost: existingRecipe.fixedCost || 0,
          costingMethod: existingRecipe.costingMethod || "fixed",
          isRecipeLocked: existingRecipe.isRecipeLocked || false,
          ingredients: existingRecipe.ingredients?.map((ingredient: any) => {
            if ('stockItemId' in ingredient) {
              return {
                type: "stock" as const,
                stockItemId: ingredient.stockItemId,
                quantity: ingredient.quantity,
              };
            } else if ('subRecipeId' in ingredient) {
              return {
                type: "subRecipe" as const,
                subRecipeId: ingredient.subRecipeId,
                quantity: ingredient.quantity,
              };
            } else {
              return ingredient;
            }
          }) || [],
        });
      } else {
        // Reset to empty recipe for this size
        form.reset({
          menuItemId,
          size: selectedSize,
          fixedCost: 0,
          costingMethod: "fixed",
          isRecipeLocked: false,
          ingredients: [],
        });
      }
    }
  }, [selectedSize, existingRecipes, menuItems, form]);

  // Handle size changes to load the correct recipe
  useEffect(() => {
    const menuItemId = form.watch("menuItemId");
    const size = form.watch("size");
    
    if (menuItemId && size && existingRecipes.length > 0) {
      const existingRecipe = existingRecipes.find(r => 
        r.menuItemId === menuItemId && r.size === size
      );
      
      if (existingRecipe) {
        // Load existing recipe data for this size
        form.reset({
          menuItemId: existingRecipe.menuItemId,
          size: existingRecipe.size || "",
          fixedCost: existingRecipe.fixedCost || 0,
          costingMethod: existingRecipe.costingMethod || "fixed",
          isRecipeLocked: existingRecipe.isRecipeLocked || false,
          ingredients: existingRecipe.ingredients?.map((ingredient: any) => {
            if ('stockItemId' in ingredient) {
              return {
                type: "stock" as const,
                stockItemId: ingredient.stockItemId,
                quantity: ingredient.quantity,
              };
            } else if ('subRecipeId' in ingredient) {
              return {
                type: "subRecipe" as const,
                subRecipeId: ingredient.subRecipeId,
                quantity: ingredient.quantity,
              };
            } else {
              return ingredient;
            }
          }) || [],
        });
      } else if (menuItemId && size) {
        // Reset to empty recipe for this size
        form.reset({
          menuItemId,
          size,
          ingredients: [{ type: "stock", stockItemId: "", quantity: 1 }],
        });
      }
    }
  }, [form.watch("menuItemId"), form.watch("size"), existingRecipes, form]);

  const handleSubmit = async (values: z.infer<typeof menuItemRecipeSchema>) => {
    console.log('[MenuItemRecipeForm] Submitting values:', values);
    console.log('[MenuItemRecipeForm] Form errors:', form.formState.errors);
    console.log('[MenuItemRecipeForm] Form is valid:', form.formState.isValid);
    setIsSubmitting(true);
    try {
      // Only transform ingredients if using recipe method and ingredients exist
      const transformedIngredients = (values.costingMethod === "recipe" && values.ingredients) 
        ? values.ingredients.map(ingredient => {
            if (ingredient.type === "stock") {
              return {
                stockItemId: ingredient.stockItemId!,
                quantity: ingredient.quantity,
              };
            } else {
              return {
                subRecipeId: ingredient.subRecipeId!,
                quantity: ingredient.quantity,
              };
            }
          })
        : []; // Empty array for fixed cost method

      const submissionData = {
        ...values,
        ingredients: transformedIngredients,
      };
      
      console.log('[MenuItemRecipeForm] Sending to parent:', submissionData);
      await onSubmit(submissionData);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubRecipeSubmit = async (data: any) => {
    try {
      await createSubRecipe(data);
      toast({
        title: "✅ Sous-recette créée",
        description: `${data.name} a été créée avec succès.`,
        duration: 2000,
      });
      setIsSubRecipeDialogOpen(false);
      // The parent component will refresh the subRecipes list automatically
    } catch (error) {
      toast({
        title: "❌ Erreur",
        description: "Impossible de créer la sous-recette.",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  return (
    <div className="space-y-3 relative z-0">
      <button 
        onClick={() => alert('TEST CLICK WORKS!')} 
        className="bg-red-500 text-white p-2 mb-2 absolute top-0 right-0 z-50"
        style={{ pointerEvents: 'auto' }}
      >
        TEST
      </button>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-3 relative z-0">
          {/* Menu Item Selection - Ultra Compact */}
          <div className="space-y-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <FormField
                control={form.control}
                name="menuItemId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs font-medium">Article</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue placeholder="Sélectionner un article" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {menuItems.map((item) => (
                          <SelectItem key={item.id} value={item.id} className="text-xs">
                            {item.name} ({item.categoryName})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {selectedMenuItem && selectedMenuItem.sizes && selectedMenuItem.sizes.length > 0 && (
                <FormField
                  control={form.control}
                  name="size"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs font-medium">Taille</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="h-8 text-xs">
                            <SelectValue placeholder="Taille" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {selectedMenuItem.sizes.map((size) => (
                            <SelectItem key={size} value={size} className="text-xs">
                              {size}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>
          </div>

          {/* Cost Management Section */}
          <div className="space-y-3 p-3 border rounded-md bg-muted/10">
            <div className="flex items-center gap-2 mb-2">
              <ChefHat className="h-4 w-4" />
              <Label className="text-sm font-medium">Gestion des Coûts</Label>
            </div>
            
            {/* Costing Method Selection */}
            <FormField
              control={form.control}
              name="costingMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xs">Méthode de Calcul des Coûts</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex gap-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="recipe" id="recipe-method" />
                        <Label htmlFor="recipe-method" className="text-xs">Calculé depuis la recette</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="fixed" id="fixed-method" />
                        <Label htmlFor="fixed-method" className="text-xs">Coût fixe</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Fixed Cost Input - Only show when fixed method is selected */}
            {form.watch("costingMethod") === "fixed" && (
              <FormField
                control={form.control}
                name="fixedCost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Coût Fixe (DA)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="Entrez le coût fixe (ex: 150.00)"
                        className="h-10 text-sm font-medium"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value;
                          const numValue = value ? parseFloat(value) : 0;
                          console.log('[FixedCostInput] Value changed to:', value, 'parsed to:', numValue);
                          field.onChange(numValue);
                        }}
                        value={field.value?.toString() || ''}
                      />
                    </FormControl>
                    <div className="text-xs text-muted-foreground">
                      Ce coût sera utilisé pour les calculs de profit au lieu du coût calculé depuis les ingrédients
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          {/* Recipe Ingredients - Ultra Compact - Only show when recipe method is selected */}
          {form.watch("costingMethod") === "recipe" && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <FormLabel className="text-xs font-medium">Ingrédients</FormLabel>
              <div className="flex gap-1">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsSubRecipeDialogOpen(true)}
                  className="h-6 px-2 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                >
                  <ChefHat className="h-3 w-3 mr-1" />
                  Nouvelle sous-recette
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => append({ type: "stock", stockItemId: "", quantity: 1 })}
                  className="h-6 px-2 text-xs"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Ajouter
                </Button>
              </div>
            </div>

            <div className="space-y-1 max-h-[350px] overflow-y-auto">
              {fields.map((field, index) => (
                <div key={field.id} className="flex items-center gap-2 p-2 border rounded-md bg-muted/20">
                  {/* Type Selection - Compact Radio */}
                  <FormField
                    control={form.control}
                    name={`ingredients.${index}.type`}
                    render={({ field }) => (
                      <FormItem className="space-y-0">
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex gap-2"
                          >
                            <div className="flex items-center space-x-1">
                              <RadioGroupItem value="stock" id={`stock-${index}`} className="h-3 w-3" />
                              <Label htmlFor={`stock-${index}`} className="text-xs flex items-center gap-1">
                                <Package className="h-3 w-3" />
                                Stock
                              </Label>
                            </div>
                            <div className="flex items-center space-x-1">
                              <RadioGroupItem value="subRecipe" id={`subrecipe-${index}`} className="h-3 w-3" />
                              <Label htmlFor={`subrecipe-${index}`} className="text-xs flex items-center gap-1">
                                <ChefHat className="h-3 w-3" />
                                Sous-recette
                              </Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Ingredient Selection */}
                  <div className="flex-1">
                    {form.watch(`ingredients.${index}.type`) === "stock" ? (
                      <FormField
                        control={form.control}
                        name={`ingredients.${index}.stockItemId`}
                        render={({ field }) => (
                          <FormItem>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="h-7 text-xs">
                                  <SelectValue placeholder="Sélectionner un ingrédient" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {stockItems.map((item) => (
                                  <SelectItem key={item.id} value={item.id} className="text-xs">
                                    <div className="flex items-center gap-2">
                                      <Package className="h-3 w-3" />
                                      {item.name}
                                      <Badge variant="outline" className="text-xs">{item.unit}</Badge>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <FormField
                        control={form.control}
                        name={`ingredients.${index}.subRecipeId`}
                        render={({ field }) => (
                          <FormItem>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="h-7 text-xs">
                                  <SelectValue placeholder="Sélectionner une sous-recette" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {subRecipes.map((subRecipe, idx) => (
                                  <SelectItem key={subRecipe._id + '-' + idx} value={subRecipe._id} className="text-xs">
                                    <div className="flex items-center gap-2">
                                      <ChefHat className="h-3 w-3" />
                                      {subRecipe.name}
                                      <Badge variant="outline" className="text-xs">{subRecipe.yield.unit}</Badge>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>

                  {/* Quantity Input */}
                  <div className="w-16">
                    <FormField
                      control={form.control}
                      name={`ingredients.${index}.quantity`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="number"
                              min="0.01"
                              step="0.01"
                              placeholder="1"
                              className="h-7 text-xs text-center"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Remove Button */}
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 text-muted-foreground hover:text-destructive"
                    onClick={() => remove(index)}
                    disabled={fields.length === 1}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>

            {form.formState.errors.ingredients?.message && (
              <p className="text-xs font-medium text-destructive">
                {form.formState.errors.ingredients.message}
              </p>
            )}

            {/* Recipe Lock Toggle - Only for recipe method */}
            <FormField
              control={form.control}
              name="isRecipeLocked"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-2">
                  <div className="space-y-0.5">
                    <FormLabel className="text-xs font-medium">Verrouiller la recette</FormLabel>
                    <div className="text-xs text-muted-foreground">
                      Marquer cette recette comme définitive
                    </div>
                  </div>
                  <FormControl>
                    <input
                      type="checkbox"
                      checked={field.value}
                      onChange={field.onChange}
                      className="h-4 w-4"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          )}

          {/* Actions - Compact */}
          <div className="flex gap-2 pt-2 relative z-10">
            <Button 
              type="button" 
              variant="outline" 
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('[Cancel Button] Clicked!');
                onCancel();
              }}
              className="flex-1 h-8 text-xs pointer-events-auto cursor-pointer"
              style={{ pointerEvents: 'auto' }}
            >
              Annuler
            </Button>
            <Button 
              type="button" 
              className="flex-1 h-8 text-xs bg-green-600 hover:bg-green-700 text-white pointer-events-auto cursor-pointer"
              style={{ pointerEvents: 'auto' }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('[Debug Submit] Manual trigger - CLICKED!');
                const values = form.getValues();
                console.log('[Debug Submit] Values:', values);
                const parsed = menuItemRecipeSchema.safeParse(values);
                console.log('[Debug Submit] Parsed:', parsed);
                if (parsed.success) {
                  handleSubmit(parsed.data);
                }
              }}
            >
              Debug Submit
            </Button>
            <Button 
              type="button" 
              disabled={isSubmitting}
              className="flex-1 h-8 text-xs pointer-events-auto cursor-pointer"
              style={{ pointerEvents: 'auto' }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('[Submit Button] Clicked!');
                console.log('[Submit Button] Form values:', form.getValues());
                console.log('[Submit Button] Form errors:', form.formState.errors);
                console.log('[Submit Button] Is submitting:', isSubmitting);
                // Trigger form submission manually
                form.handleSubmit(handleSubmit)();
              }}
            >
              {isSubmitting && <Loader2 className="mr-1 h-3 w-3 animate-spin" />}
              Enregistrer
            </Button>
          </div>
        </form>
      </Form>

      {/* Sub-Recipe Creation Dialog */}
      <Dialog open={isSubRecipeDialogOpen} onOpenChange={setIsSubRecipeDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ChefHat className="h-5 w-5 text-blue-600" />
              Créer une nouvelle sous-recette
            </DialogTitle>
          </DialogHeader>
          
          <div className="overflow-y-auto flex-1">
            <SubRecipeForm
              onSubmit={handleSubRecipeSubmit}
              onCancel={() => setIsSubRecipeDialogOpen(false)}
              stockItems={stockItems}
              isMobile={isMobile}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
